import { useEffect, useState } from "react";
import type { Swiper as SwiperType } from "swiper";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import {
  fadeIn,
  fadeInUp,
  staggerContainer,
  cardVariant,
} from "../../utils/animations";

// Import images
import slide_1 from "../../assets/images/hero/1.jpg";
import slide_2 from "../../assets/images/hero/2.jpg";
import slide_3 from "../../assets/images/hero/3.jpg";

// Import card images
import img_1_card from "../../assets/images/hero/card_1.jpg";
import img_2_card from "../../assets/images/hero/card_2.jpg";
import img_3_card from "../../assets/images/hero/card_3.jpg";
import img_4_card from "../../assets/images/hero/card_4.jpg";

export default function Hero2() {
  const { t } = useTranslation();
  const [isClient, setIsClient] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Hero slides data
  const heroSlides = [
    {
      id: 1,
      title: t("heroSection.title"),
      subtitle: t("heroSection.title2"),
      description: t("heroSection.description"),
      backgroundImage: slide_1,
      cardImage: img_1_card,
    },
    {
      id: 2,
      title: t("heroSection.title"),
      subtitle: t("monitorSection.title"),
      description: t("monitorSection.description"),
      backgroundImage: slide_2,
      cardImage: img_2_card,
    },
    {
      id: 3,
      title: t("heroSection.title"),
      subtitle: t("exploreSection.title"),
      description: t("exploreSection.description"),
      backgroundImage: slide_3,
      cardImage: img_3_card,
    },
    {
      id: 4,
      title: t("heroSection.title"),
      subtitle: t("protectSection.title"),
      description: t("protectSection.description"),
      backgroundImage: slide_1,
      cardImage: img_4_card,
    },
  ];

  useEffect(() => {
    setIsClient(true);

    const initializeSwiper = async () => {
      try {
        const [
          { default: Swiper },
          { EffectFade, EffectCoverflow, Navigation, Autoplay, Controller },
        ] = await Promise.all([import("swiper"), import("swiper/modules")]);

        const homeSwiper = new Swiper(".hero-slider", {
          modules: [EffectFade, Navigation, Controller],
          slidesPerView: 1,
          effect: "fade",
          speed: 1200,
          loop: false,
          spaceBetween: 0,
          watchSlidesProgress: true,
          initialSlide: 0,
          navigation: {
            nextEl: ".hero-slider-next",
            prevEl: ".hero-slider-prev",
          },
          on: {
            slideChange: function (swiper) {
              setCurrentSlide(swiper.activeIndex);
            },
          },
        });

        const homeBgSwiper = new Swiper(".hero-bg-slider", {
          modules: [EffectFade, Controller],
          slidesPerView: 1,
          effect: "fade",
          speed: 1200,
          loop: false,
          spaceBetween: 0,
          watchSlidesProgress: true,
        });

        const heroImgSwiper = new Swiper(".hero-img-slider", {
          modules: [EffectCoverflow, Autoplay, Controller],
          effect: "coverflow",
          speed: 1200,
          grabCursor: true,
          centeredSlides: true,
          loop: false,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 100,
            modifier: 3,
            slideShadows: true,
          },
          autoplay: {
            delay: 5000,
            disableOnInteraction: false,
          },
          breakpoints: {
            640: { slidesPerView: 3 },
            768: { slidesPerView: 3 },
            1024: { slidesPerView: 3 },
            1560: { slidesPerView: 3 },
          },
        });

        // Setup controllers after all instances are created
        homeSwiper.controller.control = [homeBgSwiper, heroImgSwiper];
        homeBgSwiper.controller.control = homeSwiper;
        heroImgSwiper.controller.control = homeSwiper;

        return [homeSwiper, homeBgSwiper, heroImgSwiper];
      } catch (error) {
        console.error("Error initializing Swiper:", error);
        return [];
      }
    };

    let swiperInstances: SwiperType[] = [];

    if (isClient) {
      initializeSwiper().then((instances) => {
        swiperInstances = instances;
      });

      return () => {
        swiperInstances.forEach((swiper) => swiper?.destroy?.(true, true));
      };
    }
  }, [isClient]);

  return (
    <motion.section
      className="hero hero-redesigned"
      initial="hidden"
      animate="visible"
      variants={fadeIn}
    >
      {/* Background Slider */}
      <motion.div className="bg" variants={fadeIn}>
        <div className="swiper hero-bg-slider">
          <div className="swiper-wrapper">
            {heroSlides.map((slide, index) => (
              <div className="swiper-slide" key={slide.id}>
                <img
                  src={slide.backgroundImage}
                  alt={`${t("heroSection.backgroundAlt")} ${index + 1}`}
                />
              </div>
            ))}
          </div>
        </div>
        {/* Enhanced gradient overlay */}
        <div className="hero-gradient-overlay"></div>
      </motion.div>

      {/* Main Content */}
      <motion.div className="hero-caption" variants={fadeInUp(0.3)}>
        <div className="container-fluid">
          <div className="row g-4 align-items-center pt-0 pt-lg-5">
            {/* Text Content */}
            <div className="col-12 col-xl-5 order-1 order-xl-0">
              <div className="swiper hero-slider">
                <div className="swiper-wrapper">
                  {heroSlides.map((slide, index) => (
                    <div className="swiper-slide" key={slide.id}>
                      <motion.div
                        className="hero-caption-card"
                        initial={{ opacity: 0, y: 50 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.8 }}
                      >
                        <motion.h1
                          className="caption-title"
                          initial={{ opacity: 0, x: -50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.7, duration: 0.6 }}
                        >
                          {slide.title}
                        </motion.h1>
                        <motion.h1
                          className="caption-title colored-title"
                          initial={{ opacity: 0, x: -50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.9, duration: 0.6 }}
                        >
                          {slide.subtitle}
                        </motion.h1>
                        <motion.p
                          className="caption-description"
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1.1, duration: 0.6 }}
                        >
                          {slide.description}
                        </motion.p>
                        <motion.div
                          className="caption-actions"
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1.3, duration: 0.6 }}
                        >
                          <a href="#services" className="btn btn-primary">
                            {t("heroSection.learnMore")}
                          </a>
                          <a href="#map" className="btn btn-outline-light ms-3">
                            {t("interactiveMap.geoExplorer")}
                          </a>
                        </motion.div>
                      </motion.div>
                    </div>
                  ))}
                </div>

                {/* Enhanced Navigation */}
                <div className="swiper-controls">
                  <div className="swiper-button-prev hero-slider-prev">
                    <i className="fas fa-chevron-left"></i>
                  </div>
                  <div className="swiper-button-next hero-slider-next">
                    <i className="fas fa-chevron-right"></i>
                  </div>
                </div>

                {/* Progress Indicators */}
                <div className="hero-progress-indicators">
                  {heroSlides.map((_, index) => (
                    <div
                      key={index}
                      className={`progress-dot ${
                        currentSlide === index ? "active" : ""
                      }`}
                    ></div>
                  ))}
                </div>
              </div>
            </div>

            {/* Image Slider */}
            <div className="col-12 col-xl-7">
              <motion.div
                className="swiper hero-img-slider"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8, duration: 1 }}
              >
                <div className="swiper-wrapper">
                  {heroSlides.map((slide, idx) => (
                    <div className="swiper-slide" key={slide.id}>
                      <div className="hero-img-card">
                        <img
                          src={slide.cardImage}
                          alt={`${t("heroSection.imageAlt")} ${idx + 1}`}
                        />
                        <div className="card-overlay">
                          <div className="card-content">
                            <h3>{slide.subtitle}</h3>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        className="scroll-indicator"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 0.8 }}
      >
        <div className="scroll-text">
          {t("common.scrollDown") || "اسحب للأسفل"}
        </div>
        <div className="scroll-arrow">
          <i className="fas fa-chevron-down"></i>
        </div>
      </motion.div>
    </motion.section>
  );
}
