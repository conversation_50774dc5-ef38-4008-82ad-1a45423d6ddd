import { useEffect } from 'react';

interface UseFullScreenSectionsOptions {
  sectionIds: string[];
  enabled?: boolean;
  minHeight?: string;
  offset?: number;
}

/**
 * Hook to make sections take full screen height
 * Similar to the NCVC website behavior
 */
export const useFullScreenSections = ({
  sectionIds,
  enabled = true,
  minHeight = '100vh',
  offset = 0
}: UseFullScreenSectionsOptions) => {
  
  useEffect(() => {
    if (!enabled) return;

    const updateSectionHeights = () => {
      const viewportHeight = window.innerHeight;
      
      sectionIds.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
          // Set minimum height to viewport height minus offset
          const calculatedHeight = Math.max(viewportHeight - offset, 500); // Minimum 500px
          section.style.minHeight = `${calculatedHeight}px`;
          section.style.display = 'flex';
          section.style.flexDirection = 'column';
          section.style.justifyContent = 'center';
        }
      });
    };

    // Initial setup
    updateSectionHeights();

    // Update on resize
    window.addEventListener('resize', updateSectionHeights);

    return () => {
      window.removeEventListener('resize', updateSectionHeights);
      
      // Cleanup - reset styles
      sectionIds.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
          section.style.minHeight = '';
          section.style.display = '';
          section.style.flexDirection = '';
          section.style.justifyContent = '';
        }
      });
    };
  }, [sectionIds, enabled, minHeight, offset]);
};

export default useFullScreenSections;
