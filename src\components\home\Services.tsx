import { useEffect, useState } from "react";
import type Swiper from "swiper";
import { Pagination, Autoplay } from "swiper/modules";
import { motion } from "framer-motion";
import { fadeIn, fadeInUp } from "../../utils/animations";
import SectionTitle from "../common/SectionTitle";

declare global {
  interface Window {
    Swiper: typeof Swiper;
  }
}

interface SwiperElement extends HTMLDivElement {
  swiper: Swiper;
}

// Import images
import MapVector from "../../assets/images/e-services/map-vector.png";
import Icon1 from "../../assets/images/e-services/icon-1.svg";
import Icon3 from "../../assets/images/e-services/icon-3.svg";
import Icon4 from "../../assets/images/e-services/icon-4.svg";
import { Link } from "react-router";
import { useTranslation } from "react-i18next";

export default function Services() {
  const { t } = useTranslation();
  const [swiperLoaded, setSwiperLoaded] = useState(false);

  useEffect(() => {
    const loadSwiper = async () => {
      if (typeof window !== "undefined") {
        try {
          const SwiperModule = await import("swiper");
          window.Swiper = SwiperModule.default;
          setSwiperLoaded(true);
        } catch (error) {
          console.error("Error loading Swiper:", error);
        }
      }
    };

    loadSwiper();
  }, []);

  useEffect(() => {
    if (!swiperLoaded) return;

    const initializeSwiper = () => {
      try {
        const swiperElement = document.querySelector(".e-services-slider");
        if (!swiperElement) return;

        const existingSwiper = (swiperElement as SwiperElement).swiper;
        if (existingSwiper) return;

        const servicesSwiper = new window.Swiper(".e-services-slider", {
          modules: [Pagination, Autoplay],
          speed: 1200,
          slidesPerView: 1,
          grabCursor: true,
          spaceBetween: 24,
          centeredSlides: true,
          initialSlide: 1,
          // loop: true,
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
            type: "bullets",
          },
          // autoplay: {
          //   delay: 3000,
          //   disableOnInteraction: false,
          // },
          breakpoints: {
            640: { slidesPerView: 1 },
            768: { slidesPerView: 2 },
            1024: { slidesPerView: 3 },
            1560: { slidesPerView: 3 },
          },
        });

        return () => {
          if (servicesSwiper && typeof servicesSwiper.destroy === "function") {
            servicesSwiper.destroy(true, true);
          }
        };
      } catch (error) {
        console.error("Error initializing Swiper:", error);
      }
    };

    const timer = setTimeout(initializeSwiper, 500);
    return () => clearTimeout(timer);
  }, [swiperLoaded]);

  return (
    <motion.section
      className="e-services padding-block"
      initial="hidden"
      animate="visible"
      variants={fadeIn}
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <motion.div className="bg" variants={fadeIn}>
        <img src={MapVector} alt="Map Vector" />
      </motion.div>
      <div className="container-fluid">
        <div className="row g-3 align-items-center mb-5">
          <motion.div
            className="col"
            variants={fadeInUp(0)}
            initial="hidden"
            animate="visible"
          >
            <SectionTitle
              // title={t("mainServices.sectionTitle")}
              title={""}
              coloredPart={
                t("mainServices.sectionTitle") +
                " " +
                t("mainServices.sectionTitleHighlight")
              }
              description={t("mainServices.sectionDescription")}
              className="m-0"
            />
          </motion.div>
          <div className="col-lg-3 g-0 d-flex justify-content-lg-end">
            <Link to="/services" className="btn btn-outline-secondary">
              {t("mainServices.exploreMore")}
            </Link>
          </div>
        </div>
        <motion.div
          className="row"
          initial="hidden"
          animate="visible"
          variants={fadeInUp(0)}
        >
          <div className="col-12">
            <div className="swiper e-services-slider">
              <div className="swiper-wrapper mb-5">
                <motion.div className="swiper-slide" variants={fadeInUp(0.1)}>
                  <Link to={"/services/explorer"} className="e-services-card">
                    <span className="service-icon">
                      <img src={Icon1} alt="Service Icon 3" />
                    </span>
                    <span className="service-title">
                      {t("mainServices.geoExplorer.title")}
                    </span>
                    <span className="service-description">
                      {t("mainServices.geoExplorer.description")}
                    </span>
                  </Link>
                </motion.div>
                <motion.div className="swiper-slide" variants={fadeInUp(0.2)}>
                  <Link to="services/rassed" className="e-services-card">
                    <span className="service-icon">
                      <img src={Icon3} alt="Service Icon 2" />
                    </span>
                    <span className="service-title">
                      {t("mainServices.wildlifeMonitor.title")}
                    </span>
                    <span className="service-description">
                      {t("mainServices.wildlifeMonitor.description")}
                    </span>
                  </Link>
                </motion.div>
                <motion.div className="swiper-slide" variants={fadeInUp(0.3)}>
                  <Link
                    to="/services/make-decision"
                    className="e-services-card"
                  >
                    <span className="service-icon">
                      <img src={Icon4} alt="Service Icon 3" />
                    </span>
                    <span className="service-title">
                      {t("mainServices.decisionSupport.title")}
                    </span>
                    <span className="service-description">
                      {t("mainServices.decisionSupport.description")}
                    </span>
                  </Link>
                </motion.div>
                {/* <div className="swiper-slide">
                  <a href="#" className="e-services-card">
                    <span className="service-icon">
                      <img src={Icon8} alt="Service Icon 1" />
                    </span>
                    <span className="service-title">
                      {t("mainServices.fieldStudies.title")}
                    </span>
                    <span className="service-description">
                      {t("mainServices.fieldStudies.description")}
                    </span>
                  </a>
                </div> */}
              </div>
              {/* <div
                className="swiper-pagination"
                style={
                  {
                    position: "relative",
                    marginTop: "2rem",
                    "--swiper-pagination-color": "#fff",
                    "--swiper-pagination-bullet-inactive-color":
                      "rgba(255,255,255,0.5)",
                    "--swiper-pagination-bullet-size": "10px",
                    "--swiper-pagination-bullet-horizontal-gap": "6px",
                  } as React.CSSProperties
                }
              ></div> */}
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
