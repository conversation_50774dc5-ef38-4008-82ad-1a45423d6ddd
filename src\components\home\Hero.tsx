import { useEffect, useState } from "react";
import type { Swiper as SwiperType } from "swiper";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { fadeIn, fadeInUp } from "../../utils/animations";

// Import images
import slide_1 from "../../assets/images/hero/الاول.png";

import img_1_card from "../../assets/images/hero/card_1.jpg";
import img_2_card from "../../assets/images/hero/card_2.jpg";
import img_3_card from "../../assets/images/hero/card_3.jpg";
import img_4_card from "../../assets/images/hero/card_4.jpg";

export default function Hero() {
  const { t } = useTranslation();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const initializeSwiper = async () => {
      try {
        const [
          { default: Swiper },
          { EffectFade, EffectCoverflow, Navigation, Autoplay, Controller },
        ] = await Promise.all([import("swiper"), import("swiper/modules")]);

        const homeSwiper = new Swiper(".hero-slider", {
          modules: [EffectFade, Navigation, Controller],
          slidesPerView: 1,
          effect: "fade",
          speed: 1200,
          loop: false,
          spaceBetween: 0,
          watchSlidesProgress: true,
          initialSlide: 0,
          navigation: {
            nextEl: ".hero-slider-next",
            prevEl: ".hero-slider-prev",
          },
          on: {
            init: function (swiper) {
              // Clear all active classes first
              swiper.slides.forEach((slide: any) => {
                slide.classList.remove("swiper-slide-active");
              });
              // Set only the first slide as active
              const firstSlide = swiper.slides[0];
              if (firstSlide) {
                firstSlide.classList.add("swiper-slide-active");
              }
            },
            slideChange: function (swiper) {
              // Clear all active classes
              swiper.slides.forEach((slide: any) => {
                slide.classList.remove("swiper-slide-active");
              });
              // Set only the current active slide
              const activeSlide = swiper.slides[swiper.activeIndex];
              if (activeSlide) {
                activeSlide.classList.add("swiper-slide-active");
              }
            },
            transitionStart: function (swiper) {
              // Ensure clean state during transition
              swiper.slides.forEach((slide: any, index: number) => {
                if (index !== swiper.activeIndex) {
                  slide.classList.remove("swiper-slide-active");
                }
              });
            },
          },
        });

        const homeBgSwiper = new Swiper(".hero-bg-slider", {
          modules: [EffectFade, Controller],
          slidesPerView: 1,
          effect: "fade",
          speed: 1200,
          loop: false,
          spaceBetween: 0,
          watchSlidesProgress: true,
        });

        const heroImgSwiper = new Swiper(".hero-img-slider", {
          modules: [EffectCoverflow, Autoplay, Controller],
          effect: "coverflow",
          speed: 1200,
          grabCursor: true,
          centeredSlides: true,
          loop: false,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 100,
            modifier: 3,
            slideShadows: true,
          },
          autoplay: {
            delay: 3000,
            disableOnInteraction: false,
          },
          breakpoints: {
            640: { slidesPerView: 3 },
            768: { slidesPerView: 3 },
            1024: { slidesPerView: 3 },
            1560: { slidesPerView: 3 },
          },
        });

        // Setup controllers after all instances are created
        homeSwiper.controller.control = [homeBgSwiper, heroImgSwiper];
        homeBgSwiper.controller.control = homeSwiper;
        heroImgSwiper.controller.control = homeSwiper;

        // Ensure first slide is active immediately after initialization
        setTimeout(() => {
          const firstSlide = homeSwiper.slides[0];
          if (
            firstSlide &&
            !firstSlide.classList.contains("swiper-slide-active")
          ) {
            homeSwiper.slides.forEach((slide: any) =>
              slide.classList.remove("swiper-slide-active")
            );
            firstSlide.classList.add("swiper-slide-active");
          }
        }, 50);

        return [homeSwiper, homeBgSwiper, heroImgSwiper];
      } catch (error) {
        console.error("Error initializing Swiper:", error);
        return [];
      }
    };

    let swiperInstances: SwiperType[] = [];

    if (isClient) {
      // Initialize immediately when client is ready
      initializeSwiper().then((instances) => {
        swiperInstances = instances;
      });

      return () => {
        swiperInstances.forEach((swiper) => swiper?.destroy?.(true, true));
      };
    }
  }, [isClient]);

  return (
    <motion.section
      className="hero"
      initial="hidden"
      animate="visible"
      variants={fadeIn}
    >
      <motion.div className="bg" variants={fadeIn}>
        <div className="swiper hero-bg-slider">
          <div className="swiper-wrapper">
            <div className="swiper-slide">
              <img src={slide_1} alt={`${t("heroSection.backgroundAlt")} 1`} />
            </div>
            <div className="swiper-slide">
              <img src={slide_1} alt={`${t("heroSection.backgroundAlt")} 2`} />
            </div>
            <div className="swiper-slide">
              <img src={slide_1} alt={`${t("heroSection.backgroundAlt")} 3`} />
            </div>
            <div className="swiper-slide">
              <img src={slide_1} alt={`${t("heroSection.backgroundAlt")} 4`} />
            </div>
          </div>
        </div>
      </motion.div>
      <motion.div className="hero-caption" variants={fadeInUp(0.3)}>
        <div className="container-fluid">
          <div className="row g-4 align-items-center pt-0 pt-lg-5">
            <div className="col-12 col-xl-4 order-1 order-xl-0">
              <div className="swiper hero-slider">
                <div className="swiper-wrapper">
                  {/* {[1, 2, 3, 4].map((i) => (
                    <div className="swiper-slide" key={i}>
                      <div className="hero-caption-card">
                        <h1 className="caption-title">
                          استكشف الحياة الفطرية فى بوابة الجيومكانية - موئل
                        </h1>
                        <h5 className="caption-sub-title">نرصد ونحلل</h5>
                        <p className="caption-description">
                          توفر البوابة خرائط وبيانات تفاعلية لدعم جهود حماية
                          البيئة والتنوع الفطري بقرارات مستنيرة.
                        </p>
                        <div className="caption-actions">
                          <a href="#" className="btn btn-primary">
                            اعرف المزيد
                          </a>
                        </div>
                      </div>
                    </div>
                  ))} */}
                  <div className="swiper-slide">
                    <div className="hero-caption-card">
                      <h1 className="caption-title">
                        {t("heroSection.title")}
                      </h1>
                      <h1 className="caption-title">
                        {t("heroSection.title2")}
                      </h1>
                      {/* <h5 className="caption-sub-title">
                        {t("heroSection.subtitle")}
                      </h5> */}
                      {/* <p className="caption-description">
                        {t("heroSection.description")}
                      </p> */}
                      {/* <div className="caption-actions">
                        <a href="#" className="btn btn-primary">
                          {t("heroSection.learnMore")}
                        </a>
                      </div> */}
                    </div>
                  </div>

                  <div className="swiper-slide">
                    <div className="hero-caption-card">
                      <h1 className="caption-title">
                        {t("heroSection.title")}
                      </h1>
                      <h1 className="caption-title">
                        {t("monitorSection.title")}
                      </h1>
                      {/* <h5 className="caption-sub-title">
                        {t("monitorSection.subtitle")}
                      </h5> */}
                      {/* <p className="caption-description">
                        {t("monitorSection.description")}
                      </p> */}
                      {/* <div className="caption-actions">
                        <a href="#" className="btn btn-primary">
                          {t("heroSection.learnMore")}
                        </a>
                      </div> */}
                    </div>
                  </div>

                  <div className="swiper-slide">
                    <div className="hero-caption-card">
                      <h1 className="caption-title">
                        {/* استكشف الحياة الفطرية فى بوابة الجيومكانية - موئل */}
                        {t("heroSection.title")}
                      </h1>
                      <h1 className="caption-title">
                        {/* استكشف الحياة الفطرية فى بوابة الجيومكانية - موئل */}
                        {t("exploreSection.title")}
                      </h1>
                      {/* <h5 className="caption-sub-title">
                        {t("exploreSection.subtitle")}
                      </h5> */}
                      {/* <p className="caption-description">
                        {t("exploreSection.description")}
                      </p> */}
                      {/* <div className="caption-actions">
                        <a href="#" className="btn btn-primary">
                          {t("heroSection.learnMore")}
                        </a>
                      </div> */}
                    </div>
                  </div>

                  <div className="swiper-slide">
                    <div className="hero-caption-card">
                      <h1 className="caption-title">
                        {/* استكشف الحياة الفطرية فى بوابة الجيومكانية - موئل */}
                        {t("heroSection.title")}
                      </h1>
                      <h1 className="caption-title">
                        {/* استكشف الحياة الفطرية فى بوابة الجيومكانية - موئل */}
                        {t("protectSection.title")}
                      </h1>
                      {/* <h5 className="caption-sub-title">
                        {t("protectSection.subtitle")}
                      </h5> */}
                      {/* <p className="caption-description">
                        {t("protectSection.description")}
                      </p>
                      <div className="caption-actions">
                        <a href="#" className="btn btn-primary">
                          {t("heroSection.learnMore")}
                        </a>
                      </div> */}
                    </div>
                  </div>
                </div>
                <div className="swiper-controls">
                  <div className="swiper-button-prev hero-slider-prev"></div>
                  <div className="swiper-button-next hero-slider-next"></div>
                </div>
              </div>
            </div>
            <div className="col-12 col-xl-8">
              <div className="swiper hero-img-slider">
                <div className="swiper-wrapper">
                  {[img_1_card, img_2_card, img_3_card, img_4_card].map(
                    (img, idx) => (
                      <div className="swiper-slide" key={idx}>
                        <div className="hero-img-card">
                          <img
                            src={img}
                            alt={`${t("heroSection.imageAlt")} ${idx + 1}`}
                          />
                        </div>
                      </div>
                    )
                  )}
                  {/* {[heroSlider1, heroSlider2, heroSlider3, heroSlider1].map(
                    (img, idx) => (
                      <div className="swiper-slide" key={idx}>
                        <div className="hero-img-card">
                          <img src={img} alt={`Hero Slider ${idx + 1}`} />
                        </div>
                      </div>
                    )
                  )} */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.section>
  );
}
