import youtubeIcon from "../../assets/images/social/youtube.svg";
import linkedinIcon from "../../assets/images/social/linkedin.svg";
import twitterIcon from "../../assets/images/social/twitter.svg";
import facebookIcon from "../../assets/images/social/facebook.svg";
import { motion } from "framer-motion";
import { useRef } from "react";
import { useTranslation } from "react-i18next";

export default function FooterLinksSection() {
  // Create refs for scroll animations
  const footerLinksRef = useRef<HTMLElement>(null);
  const footerCol1Ref = useRef<HTMLDivElement>(null);
  const footerCol2Ref = useRef<HTMLDivElement>(null);
  const footerCol3Ref = useRef<HTMLDivElement>(null);
  const footerCol4Ref = useRef<HTMLDivElement>(null);
  const copyrightRef = useRef<HTMLDivElement>(null);

  // Translation hooks
  const { t } = useTranslation();

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const listItemVariant = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  const socialIconVariant = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="page-footer-links" ref={footerLinksRef}>
      <footer className="footer" style={{ height: "100vh" }}>
        <div className="container-fluid">
          <div className="row g-3 g-xl-5">
            <div className="col-12">
              <div className="row g-4">
                <motion.div
                  className="col-12 col-sm-6 col-xl-3"
                  ref={footerCol1Ref}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={fadeInUp}
                >
                  <h5 className="footer-list-title">
                    {t("footer.importantLinks.title")}
                  </h5>
                  <motion.ul
                    className="footer-list"
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={staggerContainer}
                  >
                    <motion.li variants={listItemVariant}>
                      <a href="#">{t("footer.importantLinks.links.0")}</a>
                    </motion.li>
                    <motion.li variants={listItemVariant}>
                      <a href="#">{t("footer.importantLinks.links.1")}</a>
                    </motion.li>
                    <motion.li variants={listItemVariant}>
                      <a href="#">{t("footer.importantLinks.links.2")}</a>
                    </motion.li>
                    <motion.li variants={listItemVariant}>
                      <a href="#">{t("footer.importantLinks.links.3")}</a>
                    </motion.li>
                  </motion.ul>
                </motion.div>
                <motion.div
                  className="col-12 col-sm-6 col-xl-3"
                  ref={footerCol2Ref}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={fadeInUp}
                  transition={{ delay: 0.2 }}
                >
                  <h5 className="footer-list-title">
                    {t("footer.monitorPlatform.title")}
                  </h5>
                  <ul className="footer-list">
                    <li>
                      <a href="#">{t("footer.monitorPlatform.links.0")}</a>
                    </li>
                    <li>
                      <a href="#">{t("footer.monitorPlatform.links.1")}</a>
                    </li>
                  </ul>
                </motion.div>
                <motion.div
                  className="col-12 col-sm-6 col-xl-3"
                  ref={footerCol3Ref}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={fadeInUp}
                  transition={{ delay: 0.3 }}
                >
                  <h5 className="footer-list-title">
                    {t("footer.contactUs.title")}
                  </h5>
                  <ul className="footer-list">
                    <li>
                      <a href="#">{t("footer.contactUs.links.0")}</a>
                    </li>
                    <li>
                      <a href="#">{t("footer.contactUs.links.1")}</a>
                    </li>
                  </ul>
                </motion.div>
                <motion.div
                  className="col-12 col-sm-6 col-xl-3"
                  ref={footerCol4Ref}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={fadeInUp}
                  transition={{ delay: 0.4 }}
                >
                  <motion.ul
                    className="social-list"
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={staggerContainer}
                  >
                    <motion.li variants={socialIconVariant}>
                      <a
                        href="#"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-custom-class="custom-tooltip"
                        data-bs-title={t("footer.socialMedia.tooltips.youtube")}
                      >
                        <img src={youtubeIcon} alt="YouTube" />
                      </a>
                    </motion.li>
                    <motion.li variants={socialIconVariant}>
                      <a
                        href="#"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-custom-class="custom-tooltip"
                        data-bs-title={t(
                          "footer.socialMedia.tooltips.linkedin"
                        )}
                      >
                        <img src={linkedinIcon} alt="LinkedIn" />
                      </a>
                    </motion.li>
                    <motion.li variants={socialIconVariant}>
                      <a
                        href="#"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-custom-class="custom-tooltip"
                        data-bs-title={t("footer.socialMedia.tooltips.twitter")}
                      >
                        <img src={twitterIcon} alt="Twitter" />
                      </a>
                    </motion.li>
                    <motion.li variants={socialIconVariant}>
                      <a
                        href="#"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-custom-class="custom-tooltip"
                        data-bs-title={t(
                          "footer.socialMedia.tooltips.facebook"
                        )}
                      >
                        <img src={facebookIcon} alt="Facebook" />
                      </a>
                    </motion.li>
                  </motion.ul>
                </motion.div>
              </div>
            </div>
            <motion.div
              className="col-12"
              ref={copyrightRef}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.3 }}
              variants={fadeInUp}
              transition={{ delay: 0.5 }}
            >
              <div className="copyrights">
                {t("footer.copyright")}{" "}
                <span className="copyrights-year"></span>
              </div>
            </motion.div>
          </div>
        </div>
      </footer>
    </section>
  );
}
